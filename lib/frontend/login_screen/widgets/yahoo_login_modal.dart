/*
=======================================================
= File: yahoo_login_modal.dart
= Project: LavaMail
= Description:
=   - Yahoo login modal for email authentication
=   - Offers a selector and then the Yahoo login form
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../../core/yahoo/auth/yahoo_auth_service.dart';
import '../../../core/auth/models/user_credentials.dart';
import '../../../core/auth/services/unified_auth_service.dart';
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class YahooLoginModal extends StatefulWidget {
  final VoidCallback? onBack;

  const YahooLoginModal({super.key, this.onBack});

  @override
  State<YahooLoginModal> createState() => _YahooLoginModalState();
}

class _YahooLoginModalState extends State<YahooLoginModal> {
  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: AppLocalizations.of(context).signInToYahoo,
      titleIcon: Icons.email,
      content: YahooLoginForm(
        onBack: () {
          if (widget.onBack != null) {
            widget.onBack!();
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
      actions: [
        LavaMailOutlinedButton(
          text: AppLocalizations.of(context).cancel,
          onPressed: () {
            if (widget.onBack != null) {
              widget.onBack!();
            } else {
              Navigator.of(context).pop();
            }
          },
        ),
      ],
    );
  }
}

class YahooLoginForm extends StatefulWidget {
  final VoidCallback onBack;
  const YahooLoginForm({super.key, required this.onBack});

  @override
  State<YahooLoginForm> createState() => _YahooLoginFormState();
}

class _YahooLoginFormState extends State<YahooLoginForm> {
  final Logger _logger = Logger();
  final UnifiedAuthService _authService = UnifiedAuthService();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _appPasswordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.mail.yahoo.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'smtp.mail.yahoo.com');
  final _smtpPortController = TextEditingController(text: '465');
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _hasCredentialsSaved = false;
  bool _showDirectConnect = false;
  bool _saveCredentials = true; // Default to true for better UX
  bool _showAccountSelector = false;
  List<UserCredentials> _savedAccounts = [];

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _appPasswordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Loads saved Yahoo credentials and determines UI state
  Future<void> _loadSavedCredentials() async {
    try {
      final savedAccounts = await _authService.getSavedCredentials(EmailProvider.yahoo);

      if (mounted) {
        setState(() {
          _savedAccounts = savedAccounts;
          _hasCredentialsSaved = savedAccounts.isNotEmpty;

          if (savedAccounts.isNotEmpty) {
            if (savedAccounts.length == 1) {
              // Single account - show direct connect
              final account = savedAccounts.first;
              _showDirectConnect = true;
              _emailController.text = account.email;
              _appPasswordController.text = account.password;
              _imapHostController.text = account.imapHost;
              _imapPortController.text = account.imapPort.toString();
              _smtpHostController.text = account.smtpHost;
              _smtpPortController.text = account.smtpPort.toString();
              _useSSL = account.useSSL;
              _logger.i('Single Yahoo account loaded: ${account.email}');
            } else {
              // Multiple accounts - show selector
              _showAccountSelector = true;
              _logger.i('Multiple Yahoo accounts found: ${savedAccounts.length}');
            }
          }
        });
      }
    } catch (e, stack) {
      _logger.w('Failed to load saved Yahoo credentials: $e', stackTrace: stack);
    }
  }



  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting Yahoo login');
      final user = await YahooAuthService.signInWithCredentials(
        email: _emailController.text.trim(),
        appPassword: _appPasswordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
      );
      if (user != null) {
        if (mounted) {
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          userProvider.userType = UserType.yahoo;
          userProvider.setYahooUser(user);

          // Start data loading process as per Mermaid diagram
          await _startDataLoading(user);

          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      }
    } catch (e) {
      _logger.e('Yahoo login failed', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).loginFailed(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Handles direct connection with saved credentials
  Future<void> _handleDirectConnect() async {
    if (_emailController.text.isEmpty || _appPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).noSavedCredentialsFound),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    await _performLogin();
  }

  /// Performs the actual login process (used by both form login and direct connect)
  Future<void> _performLogin() async {
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting Yahoo login with unified service');

      final credentials = await _authService.authenticateYahoo(
        email: _emailController.text.trim(),
        appPassword: _appPasswordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
        saveCredentials: _saveCredentials,
      );

      // Convert credentials to Yahoo user for compatibility
      final user = _authService.convertToProviderUser(credentials);

      if (mounted) {
        if (_saveCredentials) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context).credentialsSavedSuccessfully),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }

        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.userType = UserType.yahoo;
        userProvider.setYahooUser(user);

        // Start data loading process as per Mermaid diagram
        await _startDataLoading(user);

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Yahoo login failed', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Starts the data loading process as defined in the Mermaid diagram
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting Yahoo data loading process...');

      // Step 1: Load metadata (placeholder for now)
      _logger.d('Loading Yahoo metadata...');

      // Step 2: Load folders/labels (placeholder for now)
      _logger.d('Loading Yahoo folders/labels...');

      // Step 3: Count attachments (placeholder for now)
      _logger.d('Counting Yahoo attachments...');

      _logger.i('Yahoo data loading completed');
    } catch (e) {
      _logger.w('Error during Yahoo data loading: $e');
      // Continue to home screen even if data loading fails
    }
  }

  /// Handle account selection from multiple accounts
  Future<void> _selectAccount(UserCredentials credentials) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Authenticate with selected credentials
      final authenticatedCredentials = await _authService.authenticateWithCredentials(credentials);

      // Convert to Yahoo user for compatibility
      final user = _authService.convertToProviderUser(authenticatedCredentials);

      if (mounted) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.userType = UserType.yahoo;
        userProvider.setYahooUser(user);

        await _startDataLoading(user);

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Failed to authenticate with selected account', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Delete an account from saved credentials
  Future<void> _deleteAccount(UserCredentials credentials) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Delete Account'),
          content: Text('Are you sure you want to delete the account ${credentials.email}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _authService.deleteCredentials(credentials.id);
        await _loadSavedCredentials(); // Reload accounts

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Account ${credentials.email} deleted'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Failed to delete account', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete account'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.email, color: Colors.purple, size: 32),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context).yahooLogin,
                style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Show account selector for multiple accounts
          if (_showAccountSelector && _savedAccounts.length > 1) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.account_circle, color: Colors.purple, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Select Yahoo Account (${_savedAccounts.length} saved)',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.purple,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ...(_savedAccounts.take(3).map((account) => Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.purple.withValues(alpha: 0.2),
                        child: Text(
                          account.email.substring(0, 1).toUpperCase(),
                          style: TextStyle(color: Colors.purple, fontWeight: FontWeight.bold),
                        ),
                      ),
                      title: Text(account.email, style: const TextStyle(fontWeight: FontWeight.w500)),
                      subtitle: Text('Last used: ${_formatDate(account.lastUsed)}'),
                      trailing: PopupMenuButton<String>(
                        onSelected: (value) {
                          if (value == 'delete') {
                            _deleteAccount(account);
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Text('Delete'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      onTap: () => _selectAccount(account),
                      dense: true,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ))),
                  if (_savedAccounts.length > 3) ...[
                    Text(
                      '... and ${_savedAccounts.length - 3} more accounts',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showAccountSelector = false;
                            });
                          },
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Add New Account'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show direct connect button if credentials are saved
          if (_showDirectConnect && _hasCredentialsSaved && !_showAccountSelector) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.purple.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context).savedCredentialsFound,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${AppLocalizations.of(context).emailAddress}: ${_emailController.text}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _handleDirectConnect,
                    icon: const Icon(Icons.login),
                    label: Text(AppLocalizations.of(context).connectWithSavedCredentials),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showDirectConnect = false;
                      });
                    },
                    child: Text(AppLocalizations.of(context).useDifferentCredentials),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show form if no direct connect or user chose different credentials, and not showing account selector
          if ((!_showDirectConnect || !_hasCredentialsSaved) && !_showAccountSelector)
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).emailAddress,
                    hintText: '<EMAIL>',
                    prefixIcon: const Icon(Icons.email_outlined),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.purple.shade50 : null,
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).enterValidEmail;
                    }
                    if (!value.contains('@')) {
                      return AppLocalizations.of(context).enterValidEmail;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _appPasswordController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).appPassword,
                    hintText: _hasCredentialsSaved ? '••••••••••••••••' : AppLocalizations.of(context).yahooAppPasswordHint,
                    prefixIcon: const Icon(Icons.lock_outlined),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                      icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                    ),
                    border: const OutlineInputBorder(),
                    filled: _hasCredentialsSaved,
                    fillColor: _hasCredentialsSaved ? Colors.purple.shade50 : null,
                  ),
                  obscureText: _obscurePassword,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context).pleaseEnterYourAppPassword;
                    }
                    if (value.length < 16) {
                      return AppLocalizations.of(context).yahooAppPasswordLength;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAdvancedSettings = !_showAdvancedSettings;
                    });
                  },
                  child: Text(_showAdvancedSettings ? AppLocalizations.of(context).hideAdvancedSettings : AppLocalizations.of(context).showAdvancedSettings),
                ),
                if (_showAdvancedSettings) ...[
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapHostController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).imapHost,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _imapPortController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).imapPort,
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpHostController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).smtpHost,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _smtpPortController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).smtpPort,
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 8),
                  SwitchListTile(
                    title: Text(AppLocalizations.of(context).useSSL),
                    value: _useSSL,
                    onChanged: (v) => setState(() => _useSSL = v),
                  ),
                ],
                const SizedBox(height: 16),

                // Checkbox for saving credentials
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _saveCredentials,
                      onChanged: (value) {
                        setState(() {
                          _saveCredentials = value ?? false;
                        });
                      },
                      activeColor: LavaMailTheme.primaryColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _saveCredentials = !_saveCredentials;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Text(
                            AppLocalizations.of(context).saveCredentialsCheckbox,
                            style: LavaMailTheme.bodyMedium,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                if (_saveCredentials) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.security,
                          size: 16,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context).credentialsStoredSecurely,
                            style: LavaMailTheme.bodySmall.copyWith(
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _handleLogin,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          AppLocalizations.of(context).loginToYahoo,
                          style: const TextStyle(fontSize: 16),
                        ),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context).yahooImportantInfo,
                  style: const TextStyle(fontSize: 11, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 