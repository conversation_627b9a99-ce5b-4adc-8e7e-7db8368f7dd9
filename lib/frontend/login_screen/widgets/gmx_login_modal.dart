/*
=======================================================
= File: gmx_login_modal.dart
= Project: LavaMail
= Description:
=   - GMX login modal for email authentication
=   - Offers a selector and then the GMX login form
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:logger/logger.dart';
import '../../../core/gmx/auth/gmx_auth_service.dart';
import '../../../core/gmx/online_mode/gmx_online_mode.dart' as gmx_online;
import '../../../core/auth/models/user_credentials.dart';
import '../../../core/auth/services/unified_auth_service.dart';
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class GmxLoginModal extends StatefulWidget {
  const GmxLoginModal({super.key});

  @override
  State<GmxLoginModal> createState() => _GmxLoginModalState();
}

class _GmxLoginModalState extends State<GmxLoginModal> {
  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: AppLocalizations.of(context)!.signInToGmx,
      titleIcon: Icons.email,
      content: GmxLoginForm(
        onBack: () => Navigator.of(context).pop(),
      ),
      actions: [
        LavaMailOutlinedButton(
          text: AppLocalizations.of(context)!.cancel,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }
}

// The GMX login form, refactored from the previous dialog
class GmxLoginForm extends StatefulWidget {
  final VoidCallback onBack;
  const GmxLoginForm({super.key, required this.onBack});

  @override
  State<GmxLoginForm> createState() => _GmxLoginFormState();
}

class _GmxLoginFormState extends State<GmxLoginForm> {
  final Logger _logger = Logger();
  final UnifiedAuthService _authService = UnifiedAuthService();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.gmx.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'mail.gmx.com');
  final _smtpPortController = TextEditingController(text: '587');
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _hasCredentialsSaved = false;
  bool _showDirectConnect = false;
  bool _saveCredentials = true; // Default to true for better UX
  bool _showAccountSelector = false;
  List<UserCredentials> _savedAccounts = [];

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Loads saved GMX credentials and determines UI state
  Future<void> _loadSavedCredentials() async {
    try {
      final savedAccounts = await _authService.getSavedCredentials(EmailProvider.gmx);

      if (mounted) {
        setState(() {
          _savedAccounts = savedAccounts;
          _hasCredentialsSaved = savedAccounts.isNotEmpty;

          if (savedAccounts.isNotEmpty) {
            if (savedAccounts.length == 1) {
              // Single account - show direct connect
              final account = savedAccounts.first;
              _showDirectConnect = true;
              _emailController.text = account.email;
              _passwordController.text = account.password;
              _imapHostController.text = account.imapHost;
              _imapPortController.text = account.imapPort.toString();
              _smtpHostController.text = account.smtpHost;
              _smtpPortController.text = account.smtpPort.toString();
              _useSSL = account.useSSL;
              _logger.i('Single GMX account loaded: ${account.email}');
            } else {
              // Multiple accounts - show selector
              _showAccountSelector = true;
              _logger.i('Multiple GMX accounts found: ${savedAccounts.length}');
            }
          }
        });
      }
    } catch (e, stack) {
      _logger.w('Failed to load saved GMX credentials: $e', stackTrace: stack);
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    await _performLogin();
  }

  /// Handles direct connection with saved credentials
  Future<void> _handleDirectConnect() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.noSavedCredentialsFound),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    await _performLogin();
  }

  /// Performs the actual login process (used by both form login and direct connect)
  Future<void> _performLogin() async {
    setState(() { _isLoading = true; });
    try {
      _logger.d('Attempting GMX login with unified service');

      final credentials = await _authService.authenticateGmx(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
        saveCredentials: _saveCredentials,
      );

      // Convert credentials to GMX user for compatibility
      final user = _authService.convertToProviderUser(credentials);

      if (mounted) {
        if (_saveCredentials) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.credentialsSavedSuccessfully),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }

        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.userType = UserType.gmx;
        userProvider.setGmxUser(user);

        // Start data loading process as per Mermaid diagram
        await _startDataLoading(user);

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('GMX login failed', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.loginFailed(e.toString())),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() { _isLoading = false; });
      }
    }
  }

  /// Starts the data loading process as defined in the Mermaid diagram
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting GMX data loading process...');

      // Create GMX online mode service instance
      final gmxService = gmx_online.GmxOnlineMode();

      // Step 1: Initialize GMX service
      _logger.d('Initializing GMX service...');
      await gmxService.initialize(user);

      // Step 2: Generate comprehensive GMX structure report (inspired by Python script)
      _logger.d('Generating comprehensive GMX structure report...');
      final structureReport = await gmxService.getDetailedStructureReport();

      if (structureReport['status'] == 'success') {
        final summary = structureReport['summary'] as Map<String, dynamic>;
        final categoryBreakdown = structureReport['categoryBreakdown'] as Map<String, dynamic>;

        _logger.i('=== GMX COMPREHENSIVE STRUCTURE REPORT ===');
        _logger.i('Discovery timestamp: ${structureReport['timestamp']}');
        _logger.i('Total folders discovered: ${summary['totalFolders']}');
        _logger.i('Selectable folders: ${summary['selectableFolders']}');
        _logger.i('Total messages across all folders: ${summary['totalMessages']}');
        _logger.i('Total unread messages: ${summary['totalUnread']}');

        _logger.i('=== CATEGORY BREAKDOWN ===');
        categoryBreakdown.forEach((category, data) {
          final categoryData = data as Map<String, dynamic>;
          _logger.i('$category: ${categoryData['count']} folders, ${categoryData['totalMessages']} messages');
        });

        // Step 3: Analyze attachments with larger sample for accuracy
        _logger.d('Analyzing GMX attachments (comprehensive sample)...');
        final attachmentAnalysis = await gmxService.getAttachmentAnalysis(maxSample: 300);
        _logger.i('GMX attachment analysis: ${attachmentAnalysis['totalAttachments']} attachments in ${attachmentAnalysis['emailsWithAttachments']} emails (sample: ${attachmentAnalysis['sampleSize']})');

        // Step 4: Cache the discovered structure for quick access
        _logger.d('Caching GMX structure data...');
        // Note: In a real implementation, you might want to cache this data
        // using SharedPreferences or Hive for faster subsequent loads

        _logger.i('=== GMX DATA LOADING COMPLETED SUCCESSFULLY ===');
        _logger.i('Final Summary:');
        _logger.i('- ${summary['totalMessages']} total emails across ${summary['totalFolders']} folders');
        _logger.i('- ${summary['totalUnread']} unread messages');
        _logger.i('- ${attachmentAnalysis['totalAttachments']} attachments detected');
        _logger.i('- Discovery method: comprehensive (inspired by Python gmx.py)');
      } else {
        _logger.w('GMX structure discovery failed: ${structureReport['error']}');
        _logger.w('Continuing with basic functionality...');
      }

    } catch (e, stack) {
      _logger.w('Error during GMX data loading: $e', stackTrace: stack);
      // Continue to home screen even if data loading fails
      // The home screen widgets will handle the case where data is not available
    }
  }

  /// Handle account selection from multiple accounts
  Future<void> _selectAccount(UserCredentials credentials) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Authenticate with selected credentials
      final authenticatedCredentials = await _authService.authenticateWithCredentials(credentials);

      // Convert to GMX user for compatibility
      final user = _authService.convertToProviderUser(authenticatedCredentials);

      if (mounted) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.userType = UserType.gmx;
        userProvider.setGmxUser(user);

        await _startDataLoading(user);

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Failed to authenticate with selected account', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Delete an account from saved credentials
  Future<void> _deleteAccount(UserCredentials credentials) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Delete Account'),
          content: Text('Are you sure you want to delete the account ${credentials.email}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _authService.deleteCredentials(credentials.id);
        await _loadSavedCredentials(); // Reload accounts

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Account ${credentials.email} deleted'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Failed to delete account', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete account'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: widget.onBack,
                icon: const Icon(Icons.arrow_back),
              ),
              const SizedBox(width: 8),
              const Icon(Icons.email, color: Colors.blue, size: 32),
              const SizedBox(width: 12),
              Text(
                AppLocalizations.of(context)!.gmxLogin,
                style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Show account selector for multiple accounts
          if (_showAccountSelector && _savedAccounts.length > 1) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: LavaMailTheme.primaryColor.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.account_circle, color: LavaMailTheme.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Select GMX Account (${_savedAccounts.length} saved)',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: LavaMailTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ...(_savedAccounts.take(3).map((account) => Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: LavaMailTheme.primaryColor.withValues(alpha: 0.2),
                        child: Text(
                          account.email.substring(0, 1).toUpperCase(),
                          style: TextStyle(color: LavaMailTheme.primaryColor, fontWeight: FontWeight.bold),
                        ),
                      ),
                      title: Text(account.email, style: const TextStyle(fontWeight: FontWeight.w500)),
                      subtitle: Text('Last used: ${_formatDate(account.lastUsed)}'),
                      trailing: PopupMenuButton<String>(
                        onSelected: (value) {
                          if (value == 'delete') {
                            _deleteAccount(account);
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Text('Delete'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      onTap: () => _selectAccount(account),
                      dense: true,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                    ),
                  ))),
                  if (_savedAccounts.length > 3) ...[
                    Text(
                      '... and ${_savedAccounts.length - 3} more accounts',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showAccountSelector = false;
                            });
                          },
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Add New Account'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: LavaMailTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show direct connect button if credentials are saved
          if (_showDirectConnect && _hasCredentialsSaved && !_showAccountSelector) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green.shade700, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.savedCredentialsFound,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${AppLocalizations.of(context)!.emailAddress}: ${_emailController.text}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _handleDirectConnect,
                    icon: const Icon(Icons.login),
                    label: Text(AppLocalizations.of(context)!.connectWithSavedCredentials),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _showDirectConnect = false;
                      });
                    },
                    child: Text(AppLocalizations.of(context)!.useDifferentCredentials),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Show form if no direct connect or user chose different credentials, and not showing account selector
          if ((!_showDirectConnect || !_hasCredentialsSaved) && !_showAccountSelector)
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                LavaMailTextFormField(
                  label: AppLocalizations.of(context)!.emailAddress,
                  hint: '<EMAIL>',
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  filled: _hasCredentialsSaved,
                  fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.enterValidEmail;
                    }
                    if (!value.contains('@')) {
                      return AppLocalizations.of(context)!.enterValidEmail;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: LavaMailTheme.spacingM),
                LavaMailTextFormField(
                  label: AppLocalizations.of(context)!.password,
                  hint: _hasCredentialsSaved ? '••••••••••••••••' : AppLocalizations.of(context)!.gmxPasswordHint,
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  filled: _hasCredentialsSaved,
                  fillColor: _hasCredentialsSaved ? LavaMailTheme.primaryColor.withValues(alpha: 0.1) : null,
                  suffixIcon: IconButton(
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                    icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)!.pleaseEnterYourPassword;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: LavaMailTheme.spacingM),
                LavaMailOutlinedButton(
                  text: AppLocalizations.of(context)!.advancedSettings,
                  icon: _showAdvancedSettings ? Icons.expand_less : Icons.expand_more,
                  onPressed: () {
                    setState(() {
                      _showAdvancedSettings = !_showAdvancedSettings;
                    });
                  },
                ),
                if (_showAdvancedSettings) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _imapHostController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.imapHost,
                            border: const OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppLocalizations.of(context)!.required;
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 80,
                        child: TextFormField(
                          controller: _imapPortController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.port,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppLocalizations.of(context)!.required;
                            }
                            final port = int.tryParse(value);
                            if (port == null || port < 1 || port > 65535) {
                              return AppLocalizations.of(context)!.invalid;
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _smtpHostController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.smtpHost,
                            border: const OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppLocalizations.of(context)!.required;
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 80,
                        child: TextFormField(
                          controller: _smtpPortController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)!.port,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppLocalizations.of(context)!.required;
                            }
                            final port = int.tryParse(value);
                            if (port == null || port < 1 || port > 65535) {
                              return AppLocalizations.of(context)!.invalid;
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const SizedBox(height: 12),
                  Text(
                    AppLocalizations.of(context)!.smtpConfiguration,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<int>(
                          title: Text(AppLocalizations.of(context)!.port587Starttls),
                          subtitle: Text(AppLocalizations.of(context)!.recommended),
                          value: 587,
                          groupValue: int.parse(_smtpPortController.text),
                          onChanged: (value) {
                            setState(() {
                              _smtpPortController.text = value.toString();
                              _useSSL = false; // STARTTLS for port 587
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<int>(
                          title: Text(AppLocalizations.of(context)!.port465SslTls),
                          value: 465,
                          groupValue: int.parse(_smtpPortController.text),
                          onChanged: (value) {
                            setState(() {
                              _smtpPortController.text = value.toString();
                              _useSSL = true; // SSL/TLS for port 465
                            });
                          },
                          dense: true,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: LavaMailTheme.spacingM),

                // Checkbox for saving credentials
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: _saveCredentials,
                      onChanged: (value) {
                        setState(() {
                          _saveCredentials = value ?? false;
                        });
                      },
                      activeColor: LavaMailTheme.primaryColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _saveCredentials = !_saveCredentials;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Text(
                            AppLocalizations.of(context)!.saveCredentialsCheckbox,
                            style: LavaMailTheme.bodyMedium,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                if (_saveCredentials) ...[
                  const SizedBox(height: LavaMailTheme.spacingS),
                  Container(
                    padding: const EdgeInsets.all(LavaMailTheme.spacingS),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(LavaMailTheme.borderRadiusS),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.security,
                          size: 16,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: LavaMailTheme.spacingS),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.credentialsStoredSecurely,
                            style: LavaMailTheme.bodySmall.copyWith(
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: LavaMailTheme.spacingL),
                LavaMailButton(
                  text: AppLocalizations.of(context)!.loginToGmx,
                  onPressed: _handleLogin,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)!.importantSetupInformation,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        AppLocalizations.of(context)!.gmxEnableImapInstructions,
                        style: TextStyle(fontSize: 11, color: Colors.black87),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.security, color: Colors.orange.shade700, size: 14),
                                const SizedBox(width: 6),
                                Text(
                                  AppLocalizations.of(context)!.ifYouHave2faEnabled,
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              AppLocalizations.of(context)!.gmxAppPasswordInstructions,
                              style: TextStyle(fontSize: 10, color: Colors.black87),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        AppLocalizations.of(context)!.gmxDefaultSettings,
                        style: TextStyle(fontSize: 10, color: Colors.black54),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
