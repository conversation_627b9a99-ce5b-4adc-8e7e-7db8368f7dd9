/*
=======================================================
= File: account_selector_widget.dart
= Project: LavaMail
= Description:
=   - Widget for selecting between multiple saved accounts for each email provider
=   - Displays saved accounts with options to add new accounts or use existing ones
=   - Supports multi-account management with visual indicators
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import '../../../core/auth/models/user_credentials.dart';
import '../../../core/auth/services/unified_auth_service.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';

/// Widget for selecting between multiple saved accounts
class AccountSelectorWidget extends StatefulWidget {
  final EmailProvider provider;
  final Function(UserCredentials) onAccountSelected;
  final VoidCallback onAddNewAccount;
  final VoidCallback? onBack;

  const AccountSelectorWidget({
    super.key,
    required this.provider,
    required this.onAccountSelected,
    required this.onAddNewAccount,
    this.onBack,
  });

  @override
  State<AccountSelectorWidget> createState() => _AccountSelectorWidgetState();
}

class _AccountSelectorWidgetState extends State<AccountSelectorWidget> {
  static final Logger _logger = Logger();
  final UnifiedAuthService _authService = UnifiedAuthService();
  
  List<UserCredentials> _savedAccounts = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSavedAccounts();
  }

  /// Load saved accounts for the provider
  Future<void> _loadSavedAccounts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final accounts = await _authService.getSavedCredentials(widget.provider);
      
      if (mounted) {
        setState(() {
          _savedAccounts = accounts;
          _isLoading = false;
        });
      }

      _logger.d('Loaded ${accounts.length} saved accounts for ${widget.provider.name}');
    } catch (e, stack) {
      _logger.e('Failed to load saved accounts for ${widget.provider.name}', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load saved accounts';
          _isLoading = false;
        });
      }
    }
  }

  /// Handle account selection and authentication
  Future<void> _selectAccount(UserCredentials credentials) async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Authenticate with the selected credentials
      final authenticatedCredentials = await _authService.authenticateWithCredentials(credentials);
      
      if (mounted) {
        widget.onAccountSelected(authenticatedCredentials);
      }
    } catch (e, stack) {
      _logger.e('Failed to authenticate with selected account', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _errorMessage = 'Authentication failed. Please check your credentials.';
          _isLoading = false;
        });
      }
    }
  }

  /// Delete an account
  Future<void> _deleteAccount(UserCredentials credentials) async {
    try {
      final confirmed = await _showDeleteConfirmation(credentials);
      if (!confirmed) return;

      await _authService.deleteCredentials(credentials.id);
      await _loadSavedAccounts(); // Reload the list
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Account ${credentials.email} deleted'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e, stack) {
      _logger.e('Failed to delete account', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete account'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  Future<bool> _showDeleteConfirmation(UserCredentials credentials) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Account'),
        content: Text('Are you sure you want to delete the account ${credentials.email}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Delete'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: 'Select ${widget.provider.name.toUpperCase()} Account',
      titleIcon: _getProviderIcon(),
      onBack: widget.onBack,
      content: _buildContent(),
      actions: _buildActions(),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(LavaMailTheme.spacingXL),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_errorMessage != null) {
      return Padding(
        padding: const EdgeInsets.all(LavaMailTheme.spacingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            LavaMailButton(
              text: 'Retry',
              icon: Icons.refresh,
              onPressed: _loadSavedAccounts,
            ),
          ],
        ),
      );
    }

    if (_savedAccounts.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(LavaMailTheme.spacingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_circle_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Text(
              'No saved accounts found',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: LavaMailTheme.spacingS),
            Text(
              'Add your first ${widget.provider.name} account to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Account list
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _savedAccounts.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final account = _savedAccounts[index];
            return _buildAccountTile(account);
          },
        ),
      ],
    );
  }

  Widget _buildAccountTile(UserCredentials account) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: LavaMailTheme.primaryColor.withValues(alpha: 0.1),
        child: Text(
          account.email.substring(0, 1).toUpperCase(),
          style: TextStyle(
            color: LavaMailTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        account.email,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(account.displayName),
          const SizedBox(height: 2),
          Row(
            children: [
              Icon(
                account.isActive ? Icons.check_circle : Icons.circle_outlined,
                size: 12,
                color: account.isActive ? Colors.green : Colors.grey,
              ),
              const SizedBox(width: 4),
              Text(
                account.isActive ? 'Active' : 'Inactive',
                style: TextStyle(
                  fontSize: 11,
                  color: account.isActive ? Colors.green : Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Last used: ${_formatDate(account.lastUsed)}',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (value) {
          switch (value) {
            case 'delete':
              _deleteAccount(account);
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, color: Colors.red),
                SizedBox(width: 8),
                Text('Delete'),
              ],
            ),
          ),
        ],
      ),
      onTap: () => _selectAccount(account),
    );
  }

  List<Widget> _buildActions() {
    return [
      LavaMailButton(
        text: 'Add New Account',
        icon: Icons.add,
        onPressed: widget.onAddNewAccount,
        variant: LavaMailButtonVariant.outlined,
      ),
    ];
  }

  IconData _getProviderIcon() {
    switch (widget.provider) {
      case EmailProvider.gmail:
        return Icons.email;
      case EmailProvider.yahoo:
        return Icons.alternate_email;
      case EmailProvider.gmx:
        return Icons.mail;
      case EmailProvider.icloud:
        return Icons.cloud;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
