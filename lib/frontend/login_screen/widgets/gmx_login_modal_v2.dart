/*
=======================================================
= File: gmx_login_modal_v2.dart
= Project: LavaMail
= Description:
=   - Enhanced GMX login modal with multi-account support using Hive
=   - Integrates with the new unified authentication system
=   - Provides account selection and management features
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import '../../../core/auth/models/user_credentials.dart';
import '../../../core/auth/services/unified_auth_service.dart';
import '../../../core/gmx/online_mode/gmx_online_mode.dart' as gmx_online;
import '../../../providers/user_provider.dart';
import '../../home_screen/home_screen.dart';
import '../../theme/app_theme.dart';
import '../../theme/widgets/common_widgets.dart';
import 'account_selector_widget.dart';

/// Enhanced GMX login modal with multi-account support
class GmxLoginModalV2 extends StatefulWidget {
  const GmxLoginModalV2({super.key});

  @override
  State<GmxLoginModalV2> createState() => _GmxLoginModalV2State();
}

class _GmxLoginModalV2State extends State<GmxLoginModalV2> {
  static final Logger _logger = Logger();
  final UnifiedAuthService _authService = UnifiedAuthService();
  
  bool _showAccountSelector = true;
  bool _showLoginForm = false;
  List<UserCredentials> _savedAccounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkSavedAccounts();
  }

  /// Check if there are saved GMX accounts
  Future<void> _checkSavedAccounts() async {
    try {
      final accounts = await _authService.getSavedCredentials(EmailProvider.gmx);
      
      if (mounted) {
        setState(() {
          _savedAccounts = accounts;
          _isLoading = false;
          
          // If no saved accounts, go directly to login form
          if (accounts.isEmpty) {
            _showAccountSelector = false;
            _showLoginForm = true;
          }
        });
      }
    } catch (e, stack) {
      _logger.e('Failed to check saved GMX accounts', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _isLoading = false;
          _showAccountSelector = false;
          _showLoginForm = true;
        });
      }
    }
  }

  /// Handle account selection
  Future<void> _onAccountSelected(UserCredentials credentials) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Convert credentials to GMX user and proceed to home screen
      final gmxUser = _authService.convertToProviderUser(credentials);
      
      if (mounted) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        userProvider.userType = UserType.gmx;
        userProvider.setGmxUser(gmxUser);

        // Start data loading process
        await _startDataLoading(gmxUser);

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e, stack) {
      _logger.e('Failed to handle account selection', error: e, stackTrace: stack);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Authentication failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show the login form for adding a new account
  void _onAddNewAccount() {
    setState(() {
      _showAccountSelector = false;
      _showLoginForm = true;
    });
  }

  /// Go back to account selector
  void _onBackToSelector() {
    setState(() {
      _showLoginForm = false;
      _showAccountSelector = true;
    });
  }

  /// Handle successful login from form
  Future<void> _onLoginSuccess(UserCredentials credentials) async {
    await _onAccountSelected(credentials);
  }

  /// Start data loading process for GMX
  Future<void> _startDataLoading(dynamic user) async {
    try {
      _logger.i('Starting GMX data loading process...');

      final gmxService = gmx_online.GmxOnlineMode();
      
      _logger.d('Initializing GMX service...');
      await gmxService.initialize(user);

      _logger.d('Generating comprehensive GMX structure report...');
      final structureReport = await gmxService.getDetailedStructureReport();

      if (structureReport['status'] == 'success') {
        final summary = structureReport['summary'] as Map<String, dynamic>;
        _logger.i('GMX data loading completed: ${summary['totalMessages']} emails across ${summary['totalFolders']} folders');
      }
    } catch (e, stack) {
      _logger.w('Error during GMX data loading: $e', stackTrace: stack);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return LavaMailModal(
        title: AppLocalizations.of(context)!.signInToGmx,
        titleIcon: Icons.email,
        content: const Center(
          child: Padding(
            padding: EdgeInsets.all(LavaMailTheme.spacingXL),
            child: CircularProgressIndicator(),
          ),
        ),
        actions: [
          LavaMailOutlinedButton(
            text: AppLocalizations.of(context)!.cancel,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      );
    }

    if (_showAccountSelector) {
      return AccountSelectorWidget(
        provider: EmailProvider.gmx,
        onAccountSelected: _onAccountSelected,
        onAddNewAccount: _onAddNewAccount,
        onBack: () => Navigator.of(context).pop(),
      );
    }

    if (_showLoginForm) {
      return GmxLoginFormV2(
        onBack: _savedAccounts.isNotEmpty ? _onBackToSelector : () => Navigator.of(context).pop(),
        onLoginSuccess: _onLoginSuccess,
      );
    }

    return Container(); // Should never reach here
  }
}

/// GMX login form with new authentication system
class GmxLoginFormV2 extends StatefulWidget {
  final VoidCallback onBack;
  final Function(UserCredentials) onLoginSuccess;

  const GmxLoginFormV2({
    super.key,
    required this.onBack,
    required this.onLoginSuccess,
  });

  @override
  State<GmxLoginFormV2> createState() => _GmxLoginFormV2State();
}

class _GmxLoginFormV2State extends State<GmxLoginFormV2> {
  static final Logger _logger = Logger();
  final UnifiedAuthService _authService = UnifiedAuthService();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _imapHostController = TextEditingController(text: 'imap.gmx.com');
  final _imapPortController = TextEditingController(text: '993');
  final _smtpHostController = TextEditingController(text: 'mail.gmx.com');
  final _smtpPortController = TextEditingController(text: '587');
  
  bool _isLoading = false;
  bool _useSSL = true;
  bool _showAdvancedSettings = false;
  bool _obscurePassword = true;
  bool _saveCredentials = true; // Default to true for better UX

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _imapHostController.dispose();
    _imapPortController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    super.dispose();
  }

  /// Handle login form submission
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      _logger.d('Attempting GMX authentication with unified service');
      
      final credentials = await _authService.authenticateGmx(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        imapHost: _imapHostController.text.trim(),
        imapPort: int.parse(_imapPortController.text),
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text),
        useSSL: _useSSL,
        saveCredentials: _saveCredentials,
      );

      if (mounted) {
        if (_saveCredentials) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.credentialsSavedSuccessfully),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        
        widget.onLoginSuccess(credentials);
      }
    } catch (e, stack) {
      _logger.e('GMX authentication failed', error: e, stackTrace: stack);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login failed: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LavaMailModal(
      title: AppLocalizations.of(context)!.signInToGmx,
      titleIcon: Icons.email,
      onBack: widget.onBack,
      content: _buildForm(),
      actions: [
        LavaMailOutlinedButton(
          text: AppLocalizations.of(context)!.cancel,
          onPressed: () => Navigator.of(context).pop(),
        ),
        LavaMailButton(
          text: _isLoading ? 'Connecting...' : AppLocalizations.of(context)!.signIn,
          icon: _isLoading ? null : Icons.login,
          onPressed: _isLoading ? null : _handleLogin,
          isLoading: _isLoading,
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          LavaMailTextFormField(
            label: AppLocalizations.of(context)!.emailAddress,
            hint: '<EMAIL>',
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context)!.enterValidEmail;
              }
              if (!value.contains('@')) {
                return AppLocalizations.of(context)!.enterValidEmail;
              }
              return null;
            },
          ),
          const SizedBox(height: LavaMailTheme.spacingM),
          LavaMailTextFormField(
            label: AppLocalizations.of(context)!.password,
            hint: AppLocalizations.of(context)!.gmxPasswordHint,
            controller: _passwordController,
            obscureText: _obscurePassword,
            suffixIcon: IconButton(
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
              icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.of(context)!.pleaseEnterYourPassword;
              }
              return null;
            },
          ),
          const SizedBox(height: LavaMailTheme.spacingM),
          
          // Save credentials checkbox
          Row(
            children: [
              Checkbox(
                value: _saveCredentials,
                onChanged: (value) {
                  setState(() {
                    _saveCredentials = value ?? false;
                  });
                },
                activeColor: LavaMailTheme.primaryColor,
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _saveCredentials = !_saveCredentials;
                    });
                  },
                  child: Text(
                    'Save credentials for quick access',
                    style: LavaMailTheme.bodyMedium,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: LavaMailTheme.spacingM),
          
          // Advanced settings toggle
          LavaMailOutlinedButton(
            text: AppLocalizations.of(context)!.advancedSettings,
            icon: _showAdvancedSettings ? Icons.expand_less : Icons.expand_more,
            onPressed: () {
              setState(() {
                _showAdvancedSettings = !_showAdvancedSettings;
              });
            },
          ),
          
          // Advanced settings form
          if (_showAdvancedSettings) ...[
            const SizedBox(height: LavaMailTheme.spacingM),
            Row(
              children: [
                Expanded(
                  child: LavaMailTextFormField(
                    label: AppLocalizations.of(context)!.imapHost,
                    controller: _imapHostController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(context)!.required;
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: LavaMailTheme.spacingS),
                SizedBox(
                  width: 80,
                  child: LavaMailTextFormField(
                    label: AppLocalizations.of(context)!.port,
                    controller: _imapPortController,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(context)!.required;
                      }
                      final port = int.tryParse(value);
                      if (port == null || port < 1 || port > 65535) {
                        return AppLocalizations.of(context)!.invalid;
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Row(
              children: [
                Expanded(
                  child: LavaMailTextFormField(
                    label: AppLocalizations.of(context)!.smtpHost,
                    controller: _smtpHostController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(context)!.required;
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: LavaMailTheme.spacingS),
                SizedBox(
                  width: 80,
                  child: LavaMailTextFormField(
                    label: AppLocalizations.of(context)!.port,
                    controller: _smtpPortController,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(context)!.required;
                      }
                      final port = int.tryParse(value);
                      if (port == null || port < 1 || port > 65535) {
                        return AppLocalizations.of(context)!.invalid;
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: LavaMailTheme.spacingM),
            Row(
              children: [
                Checkbox(
                  value: _useSSL,
                  onChanged: (value) {
                    setState(() {
                      _useSSL = value ?? true;
                    });
                  },
                  activeColor: LavaMailTheme.primaryColor,
                ),
                Text(
                  'Use SSL/TLS',
                  style: LavaMailTheme.bodyMedium,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
