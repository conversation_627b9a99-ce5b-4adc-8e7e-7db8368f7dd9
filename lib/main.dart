import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

import 'utils/helpers/security/encryption_service.dart' as encryption;
import 'frontend/home_screen/home_screen.dart';
import 'frontend/login_screen/login_screen.dart';
import 'frontend/theme/app_theme.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'utils/app_settings.dart';
import 'package:provider/provider.dart';
import 'providers/user_provider.dart';
import 'frontend/settings_screen/settings_screen.dart';
import 'utils/services/external/admob_service.dart';
import 'utils/services/lifecycle/app_lifecycle_manager.dart';
import 'core/gmx/auth/gmx_auth_service.dart';
import 'core/yahoo/auth/yahoo_auth_service.dart';
import 'core/icloud/auth/icloud_auth_service.dart';
import 'core/auth/models/user_credentials.dart';


// Global locale notifier - will be replaced by LocaleNotifier
late ValueNotifier<Locale> appLocale;

// Create a global instance of FlutterSecureStorage
const FlutterSecureStorage secureStorage = FlutterSecureStorage();

// Global encryption key for Hive
late List<int> encryptionKey;

/// Cleans corrupted Hive data that might have incompatible typeId
Future<void> cleanCorruptedHiveData() async {
  try {
    final appDocumentDir = await getApplicationDocumentsDirectory();
    final hiveDir = Directory(appDocumentDir.path);

    // Delete all .hive files to clear corrupted data
    final files = hiveDir.listSync();
    for (final file in files) {
      if (file.path.endsWith('.hive') || file.path.endsWith('.lock')) {
        try {
          await file.delete();
          Logger().i('Deleted corrupted Hive file:  [32m${file.path} [0m');
        } catch (e) {
          Logger().w('Could not delete file ${file.path}: $e');
        }
      }
    }

    Logger().i('Hive cache cleaned successfully');
  } catch (e) {
    Logger().e('Error cleaning Hive cache: $e');
  }
}

/// Initialize Hive adapters for data models
Future<void> _initializeHiveAdapters() async {
  try {
    // Register UserCredentials adapter
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(UserCredentialsAdapter());
    }
    // Register EmailProvider adapter
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(EmailProviderAdapter());
    }
    Logger().i('Hive adapters initialized successfully');
  } catch (e) {
    Logger().e('Failed to initialize Hive adapters', error: e);
    rethrow;
  }
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize Hive
  try {
    final appDocumentDir = await getApplicationDocumentsDirectory();
    await Hive.initFlutter(appDocumentDir.path);
  } catch (e) {
    Logger().e('Failed to initialize Hive path', error: e);
    // Fallback or alternative path initialization if necessary
    // This is a critical failure point, consider how to handle it robustly.
  }

  // Initialize enhanced encryption service
  await encryption.EncryptionService.initialize();

  // Set the global encryption key from the enhanced service
  encryptionKey = encryption.EncryptionService.getCurrentEncryptionKey();

  // Clean corrupted Hive data if needed
  await cleanCorruptedHiveData();

  // Initialize Hive adapters
  await _initializeHiveAdapters();

  // Settings box will be opened when needed

  // Initialize locale notifier with saved locale
  appLocale = ValueNotifier<Locale>(await AppSettings.getSavedLocale());

  // Application initialized in ONLINE mode by default
  Logger().i('Application initialized successfully in ONLINE mode');

  // Initialize background sync service (to be implemented)
  try {
    // Background sync initialization will be handled by the sync service
  } catch (e) {
    Logger().e('Failed to initialize sync service', error: e);
    // Continue anyway, as this is not critical for the app to function
  }

  // Initialize notifications
  await NotificationService().initialize();

  // Initialize AdMob service (Android only)
  try {
    final appOpenAdManager = AppOpenAdManager();
    final adMobInitialized = await appOpenAdManager.initialize();
    if (adMobInitialized) {
      Logger().i('AppOpenAdManager initialized successfully');
      // Initialize app lifecycle reactor for App Open Ads
      final appLifecycleReactor = AppLifecycleReactor(appOpenAdManager: appOpenAdManager);
      appLifecycleReactor.listenToAppStateChanges();
    } else {
      Logger().i('AppOpenAdManager not initialized (not Android or failed)');
    }
  } catch (e) {
    Logger().e('Failed to initialize AppOpenAdManager', error: e);
    // Continue anyway, as this is not critical for the app to function
  }

  // Configure screen orientation
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Configure status bar
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  // Enable back button callback for Android 13+
  SystemUiMode.edgeToEdge;

  runApp(
    ChangeNotifierProvider(
      create: (_) => UserProvider(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Locale>(
      valueListenable: appLocale,
      builder: (context, locale, _) {
        return MaterialApp(
          title: 'LavaMail',
          debugShowCheckedModeBanner: false,
          theme: LavaMailTheme.lightTheme(),  // Appliquer le thème global
          locale: locale,
          supportedLocales: AppLocalizations.supportedLocales,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          home: const SplashScreen(),
          routes: {
            '/login': (context) => const LoginScreen(),
            '/home': (context) => const HomeScreen(),
            '/settings': (context) {
              final user = Provider.of<UserProvider>(context, listen: false).user;
              if (user == null) {
                return const LoginScreen();
              }
              return SettingsScreen(user: user);
            },
          },
        );
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> {
  final Logger _logger = Logger();
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'https://www.googleapis.com/auth/gmail.readonly'],
  );

  GoogleSignInAccount? _currentUser;

  @override
  void initState() {
    super.initState();
    _checkCurrentUser();
  }

  /// Checks if a user is already logged in and navigates accordingly
  Future<void> _checkCurrentUser() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Check for saved users from all providers
      bool userFound = false;

      // Check Gmail user
      _currentUser = await _googleSignIn.signInSilently();
      if (_currentUser != null) {
        _logger.i('Gmail user already signed in: ${_currentUser!.email}');

        // Check if Firebase user is also signed in
        final firebaseUser = FirebaseAuth.instance.currentUser;
        if (firebaseUser == null) {
          await _signInWithGoogle(_currentUser!);
        }

        userProvider.setUser(_currentUser);
        userProvider.userType = UserType.gmail;
        userFound = true;
      }

      // Check GMX user (if no Gmail user found)
      if (!userFound) {
        try {
          final gmxUser = await GmxAuthService.loadSavedUser();
          if (gmxUser != null) {
            _logger.i('GMX user found: ${gmxUser.email}');
            userProvider.setGmxUser(gmxUser);
            userProvider.userType = UserType.gmx;
            userFound = true;
          }
        } catch (e) {
          _logger.d('No saved GMX user found: $e');
        }
      }

      // Check Yahoo user (if no other user found)
      if (!userFound) {
        try {
          final yahooUser = await YahooAuthService.loadSavedUser();
          if (yahooUser != null) {
            _logger.i('Yahoo user found: ${yahooUser.email}');
            userProvider.setYahooUser(yahooUser);
            userProvider.userType = UserType.yahoo;
            userFound = true;
          }
        } catch (e) {
          _logger.d('No saved Yahoo user found: $e');
        }
      }

      // Check iCloud user (if no other user found)
      if (!userFound) {
        try {
          final icloudUser = await IcloudAuthService.loadSavedUser();
          if (icloudUser != null) {
            _logger.i('iCloud user found: ${icloudUser.email}');
            userProvider.setIcloudUser(icloudUser);
            userProvider.userType = UserType.icloud;
            userFound = true;
          }
        } catch (e) {
          _logger.d('No saved iCloud user found: $e');
        }
      }

      if (userFound && mounted) {
        _logger.i('User found, navigating to home screen');
        await _showAppOpenAdAndNavigate();
      } else {
        // No user logged in, redirect to login screen
        _logger.i('No saved user found, redirecting to login');
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/login');
        }
      }
    } catch (e) {
      _logger.e('Error checking current user', error: e);
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  /// Signs in the user to Firebase using Google credentials
  Future<void> _signInWithGoogle(GoogleSignInAccount googleUser) async {
    try {
      final googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        _logger.e('Google authentication did not return accessToken or idToken.');
        throw Exception('Failed to get Google auth tokens.');
      }

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      await FirebaseAuth.instance.signInWithCredential(credential);
      _logger.i('User signed in to Firebase: ${googleUser.email}');
    } catch (e) {
      _logger.e('Error signing in with Google to Firebase', error: e);
      // Handle Firebase sign-in error, e.g., by signing out Google user
      await _googleSignIn.signOut(); // Sign out from Google if Firebase fails
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  /// Shows App Open Ad and then navigates to HomeScreen
  Future<void> _showAppOpenAdAndNavigate() async {
    try {
      _logger.i('=== BEGIN APP OPEN AD DISPLAY ===');

      // Check if an ad is available
      final adManager = AppOpenAdManager();
      final isAdAvailable = adManager.isAdAvailable;
      _logger.i('AdMob: Ad available: $isAdAvailable');

      if (isAdAvailable) {
        _logger.i('AdMob: Attempting to display ad...');
        adManager.showAdIfAvailable();

        // Wait for the ad to display
        await Future.delayed(const Duration(milliseconds: 3000));
        _logger.i('AdMob: Wait delay finished');
      } else {
        _logger.w('AdMob: No ad available to display');
        // Try to load a new ad
        adManager.loadAd();
        _logger.i('AdMob: Loading new ad...');

        // Wait for the ad to load
        await Future.delayed(const Duration(milliseconds: 2000));

        // Try again
        if (adManager.isAdAvailable) {
          _logger.i('AdMob: New ad loaded, attempting to display...');
          adManager.showAdIfAvailable();
          await Future.delayed(const Duration(milliseconds: 3000));
        } else {
          _logger.w('AdMob: Unable to load an ad');
        }
      }
    } catch (e) {
      _logger.e('AdMob: Error displaying ad', error: e);
    }

    _logger.i('=== NAVIGATING TO HOME SCREEN ===');
    // Navigate to HomeScreen in all cases
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const HomeScreen(),
        ),
      );
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo or icon
            Icon(Icons.email, size: 80, color: Theme.of(context).primaryColor),
            const SizedBox(height: 24),
            // App name
            Text(
              AppLocalizations.of(context).appTitle,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 48),
            // Loading indicator
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(AppLocalizations.of(context).loading, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }
}

/// Singleton service to manage offline/online data mode
class DataModeService {
  static final DataModeService _instance = DataModeService._internal();
  factory DataModeService() => _instance;
  DataModeService._internal();

  bool _isOfflineMode = false;
  bool get isOfflineMode => _isOfflineMode;

  void setOfflineMode(bool value) {
    _isOfflineMode = value;
  }
}

/// Singleton service to manage notifications
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  /// Initializes notification service
  Future<void> initialize() async {
    // TODO: Implement notification initialization
  }

  /// Shows a notification with the given title and body
  Future<void> showNotification(String title, String body) async {
    // TODO: Implement notification display
  }
}
