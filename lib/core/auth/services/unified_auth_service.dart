/*
=======================================================
= File: unified_auth_service.dart
= Project: LavaMail
= Description:
=   - Unified authentication service that works with the new Hive-based credentials system
=   - Provides multi-account support for all email providers
=   - Handles authentication, credential saving, and account switching
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:logger/logger.dart';
import '../models/user_credentials.dart';
import '../services/credentials_manager.dart';
import '../../gmx/auth/gmx_auth_service.dart';
import '../../yahoo/auth/yahoo_auth_service.dart';
import '../../icloud/auth/icloud_auth_service.dart';
import '../../gmx/models/gmx_user.dart';
import '../../yahoo/models/yahoo_user.dart';
import '../../icloud/models/icloud_user.dart';

/// Unified authentication service for all email providers
class UnifiedAuthService {
  static final UnifiedAuthService _instance = UnifiedAuthService._internal();
  factory UnifiedAuthService() => _instance;
  UnifiedAuthService._internal();

  static final Logger _logger = Logger();
  final CredentialsManager _credentialsManager = CredentialsManager();

  /// Authenticate with GMX using credentials
  Future<UserCredentials> authenticateGmx({
    required String email,
    required String password,
    String? imapHost,
    int? imapPort,
    String? smtpHost,
    int? smtpPort,
    bool? useSSL,
    bool saveCredentials = true,
  }) async {
    try {
      _logger.d('Authenticating GMX user: $email');

      // Use existing GMX auth service for actual authentication
      final gmxUser = await GmxAuthService.signInWithCredentials(
        email: email,
        password: password,
        imapHost: imapHost ?? 'imap.gmx.com',
        imapPort: imapPort ?? 993,
        smtpHost: smtpHost ?? 'mail.gmx.com',
        smtpPort: smtpPort ?? 587,
        useSSL: useSSL ?? true,
      );

      // Convert to UserCredentials and save
      final credentials = UserCredentials.fromGmxUser(gmxUser);
      
      if (saveCredentials) {
        await _credentialsManager.saveCredentials(credentials);
        _logger.i('GMX credentials saved for: $email');
      }

      return credentials;
    } catch (e, stack) {
      _logger.e('GMX authentication failed for: $email', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Authenticate with Yahoo using credentials
  Future<UserCredentials> authenticateYahoo({
    required String email,
    required String appPassword,
    String? imapHost,
    int? imapPort,
    String? smtpHost,
    int? smtpPort,
    bool? useSSL,
    bool saveCredentials = true,
  }) async {
    try {
      _logger.d('Authenticating Yahoo user: $email');

      // Use existing Yahoo auth service for actual authentication
      final yahooUser = await YahooAuthService.signInWithCredentials(
        email: email,
        appPassword: appPassword,
        imapHost: imapHost ?? 'imap.mail.yahoo.com',
        imapPort: imapPort ?? 993,
        smtpHost: smtpHost ?? 'smtp.mail.yahoo.com',
        smtpPort: smtpPort ?? 465,
        useSSL: useSSL ?? true,
      );

      // Convert to UserCredentials and save
      final credentials = UserCredentials.fromYahooUser(yahooUser);
      
      if (saveCredentials) {
        await _credentialsManager.saveCredentials(credentials);
        _logger.i('Yahoo credentials saved for: $email');
      }

      return credentials;
    } catch (e, stack) {
      _logger.e('Yahoo authentication failed for: $email', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Authenticate with iCloud using credentials
  Future<UserCredentials> authenticateIcloud({
    required String email,
    required String appPassword,
    String? imapHost,
    int? imapPort,
    String? smtpHost,
    int? smtpPort,
    bool? useSSL,
    bool saveCredentials = true,
  }) async {
    try {
      _logger.d('Authenticating iCloud user: $email');

      // Use existing iCloud auth service for actual authentication
      final icloudUser = await IcloudAuthService.signInWithCredentials(
        email: email,
        appPassword: appPassword,
        imapHost: imapHost ?? 'imap.mail.me.com',
        imapPort: imapPort ?? 993,
        smtpHost: smtpHost ?? 'smtp.mail.me.com',
        smtpPort: smtpPort ?? 587,
        useSSL: useSSL ?? true,
      );

      // Convert to UserCredentials and save
      final credentials = UserCredentials.fromIcloudUser(icloudUser);
      
      if (saveCredentials) {
        await _credentialsManager.saveCredentials(credentials);
        _logger.i('iCloud credentials saved for: $email');
      }

      return credentials;
    } catch (e, stack) {
      _logger.e('iCloud authentication failed for: $email', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Authenticate using existing credentials
  Future<UserCredentials> authenticateWithCredentials(UserCredentials credentials) async {
    try {
      _logger.d('Authenticating with existing credentials: ${credentials.provider.name} - ${credentials.email}');

      UserCredentials updatedCredentials;

      switch (credentials.provider) {
        case EmailProvider.gmx:
          updatedCredentials = await authenticateGmx(
            email: credentials.email,
            password: credentials.password,
            imapHost: credentials.imapHost,
            imapPort: credentials.imapPort,
            smtpHost: credentials.smtpHost,
            smtpPort: credentials.smtpPort,
            useSSL: credentials.useSSL,
            saveCredentials: false, // Don't save again
          );
          break;
        case EmailProvider.yahoo:
          updatedCredentials = await authenticateYahoo(
            email: credentials.email,
            appPassword: credentials.password,
            imapHost: credentials.imapHost,
            imapPort: credentials.imapPort,
            smtpHost: credentials.smtpHost,
            smtpPort: credentials.smtpPort,
            useSSL: credentials.useSSL,
            saveCredentials: false, // Don't save again
          );
          break;
        case EmailProvider.icloud:
          updatedCredentials = await authenticateIcloud(
            email: credentials.email,
            appPassword: credentials.password,
            imapHost: credentials.imapHost,
            imapPort: credentials.imapPort,
            smtpHost: credentials.smtpHost,
            smtpPort: credentials.smtpPort,
            useSSL: credentials.useSSL,
            saveCredentials: false, // Don't save again
          );
          break;
        case EmailProvider.gmail:
          throw UnsupportedError('Gmail authentication not supported in unified service yet');
      }

      // Update last used timestamp
      await _credentialsManager.updateLastUsed(credentials.id);
      
      return updatedCredentials;
    } catch (e, stack) {
      _logger.e('Authentication failed with existing credentials', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get saved credentials for a provider
  Future<List<UserCredentials>> getSavedCredentials(EmailProvider provider) async {
    return await _credentialsManager.getCredentialsForProvider(provider);
  }

  /// Get the most recently used credentials for a provider
  Future<UserCredentials?> getLastUsedCredentials(EmailProvider provider) async {
    return await _credentialsManager.getLastUsedCredentials(provider);
  }

  /// Get all active credentials for a provider
  Future<List<UserCredentials>> getActiveCredentials(EmailProvider provider) async {
    return await _credentialsManager.getActiveCredentials(provider);
  }

  /// Delete credentials
  Future<void> deleteCredentials(String credentialsId) async {
    await _credentialsManager.deleteCredentials(credentialsId);
  }

  /// Set credentials as active/inactive
  Future<void> setCredentialsActive(String credentialsId, bool active) async {
    await _credentialsManager.setCredentialsActive(credentialsId, active);
  }

  /// Get credentials statistics
  Future<Map<String, dynamic>> getStatistics() async {
    return await _credentialsManager.getStatistics();
  }

  /// Convert UserCredentials back to provider-specific user objects
  dynamic convertToProviderUser(UserCredentials credentials) {
    switch (credentials.provider) {
      case EmailProvider.gmx:
        return GmxUser(
          email: credentials.email,
          password: credentials.password,
          displayName: credentials.displayName,
          imapHost: credentials.imapHost,
          imapPort: credentials.imapPort,
          smtpHost: credentials.smtpHost,
          smtpPort: credentials.smtpPort,
          useSSL: credentials.useSSL,
          lastLogin: credentials.lastUsed,
        );
      case EmailProvider.yahoo:
        return YahooUser(
          email: credentials.email,
          appPassword: credentials.password,
          displayName: credentials.displayName,
          imapHost: credentials.imapHost,
          imapPort: credentials.imapPort,
          smtpHost: credentials.smtpHost,
          smtpPort: credentials.smtpPort,
          useSSL: credentials.useSSL,
          lastLogin: credentials.lastUsed,
        );
      case EmailProvider.icloud:
        return IcloudUser(
          email: credentials.email,
          appPassword: credentials.password,
          displayName: credentials.displayName,
          imapHost: credentials.imapHost,
          imapPort: credentials.imapPort,
          smtpHost: credentials.smtpHost,
          smtpPort: credentials.smtpPort,
          useSSL: credentials.useSSL,
          lastLogin: credentials.lastUsed,
        );
      case EmailProvider.gmail:
        throw UnsupportedError('Gmail user conversion not supported yet');
    }
  }
}
