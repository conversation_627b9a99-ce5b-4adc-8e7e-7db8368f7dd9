/*
=======================================================
= File: credentials_manager.dart
= Project: LavaMail
= Description:
=   - Centralized service for managing encrypted user credentials with Hive
=   - Supports multi-account management for all email providers
=   - Provides secure storage, retrieval, and migration from FlutterSecureStorage
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:hive/hive.dart';
import 'package:logger/logger.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_credentials.dart';
import '../../../utils/services/storage/hive_manager.dart';

/// Service for managing encrypted user credentials with multi-account support
class CredentialsManager {
  static final CredentialsManager _instance = CredentialsManager._internal();
  factory CredentialsManager() => _instance;
  CredentialsManager._internal();

  static final Logger _logger = Logger();
  static const String _boxName = 'user_credentials';
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  
  Box<UserCredentials>? _credentialsBox;

  /// Initialize the credentials manager
  Future<void> initialize() async {
    try {
      _credentialsBox = await HiveManager().getBox(_boxName) as Box<UserCredentials>;
      _logger.i('CredentialsManager initialized successfully');
      
      // Perform migration from FlutterSecureStorage if needed
      await _migrateFromSecureStorage();
    } catch (e, stack) {
      _logger.e('Failed to initialize CredentialsManager', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get the credentials box (ensure it's initialized)
  Future<Box<UserCredentials>> _getBox() async {
    if (_credentialsBox == null || !_credentialsBox!.isOpen) {
      await initialize();
    }
    return _credentialsBox!;
  }

  /// Save user credentials
  Future<void> saveCredentials(UserCredentials credentials) async {
    try {
      final box = await _getBox();
      await box.put(credentials.id, credentials);
      _logger.i('Saved credentials for ${credentials.provider.name}: ${credentials.email}');
    } catch (e, stack) {
      _logger.e('Failed to save credentials', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get all credentials for a specific provider
  Future<List<UserCredentials>> getCredentialsForProvider(EmailProvider provider) async {
    try {
      final box = await _getBox();
      final allCredentials = box.values.where((cred) => cred.provider == provider).toList();
      
      // Sort by last used (most recent first)
      allCredentials.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));
      
      _logger.d('Found ${allCredentials.length} credentials for ${provider.name}');
      return allCredentials;
    } catch (e, stack) {
      _logger.e('Failed to get credentials for provider ${provider.name}', error: e, stackTrace: stack);
      return [];
    }
  }

  /// Get the most recently used credentials for a provider
  Future<UserCredentials?> getLastUsedCredentials(EmailProvider provider) async {
    try {
      final credentials = await getCredentialsForProvider(provider);
      return credentials.isNotEmpty ? credentials.first : null;
    } catch (e, stack) {
      _logger.e('Failed to get last used credentials for ${provider.name}', error: e, stackTrace: stack);
      return null;
    }
  }

  /// Get all active credentials for a provider
  Future<List<UserCredentials>> getActiveCredentials(EmailProvider provider) async {
    try {
      final credentials = await getCredentialsForProvider(provider);
      return credentials.where((cred) => cred.isActive).toList();
    } catch (e, stack) {
      _logger.e('Failed to get active credentials for ${provider.name}', error: e, stackTrace: stack);
      return [];
    }
  }

  /// Get credentials by ID
  Future<UserCredentials?> getCredentialsById(String id) async {
    try {
      final box = await _getBox();
      return box.get(id);
    } catch (e, stack) {
      _logger.e('Failed to get credentials by ID: $id', error: e, stackTrace: stack);
      return null;
    }
  }

  /// Update last used timestamp for credentials
  Future<void> updateLastUsed(String credentialsId) async {
    try {
      final box = await _getBox();
      final credentials = box.get(credentialsId);
      if (credentials != null) {
        credentials.updateLastUsed();
        _logger.d('Updated last used for credentials: $credentialsId');
      }
    } catch (e, stack) {
      _logger.e('Failed to update last used for credentials: $credentialsId', error: e, stackTrace: stack);
    }
  }

  /// Set credentials as active/inactive
  Future<void> setCredentialsActive(String credentialsId, bool active) async {
    try {
      final box = await _getBox();
      final credentials = box.get(credentialsId);
      if (credentials != null) {
        credentials.setActive(active);
        _logger.d('Set credentials $credentialsId active: $active');
      }
    } catch (e, stack) {
      _logger.e('Failed to set credentials active status: $credentialsId', error: e, stackTrace: stack);
    }
  }

  /// Delete credentials
  Future<void> deleteCredentials(String credentialsId) async {
    try {
      final box = await _getBox();
      await box.delete(credentialsId);
      _logger.i('Deleted credentials: $credentialsId');
    } catch (e, stack) {
      _logger.e('Failed to delete credentials: $credentialsId', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Delete all credentials for a provider
  Future<void> deleteAllCredentialsForProvider(EmailProvider provider) async {
    try {
      final box = await _getBox();
      final keysToDelete = <String>[];
      
      for (final entry in box.toMap().entries) {
        if (entry.value.provider == provider) {
          keysToDelete.add(entry.key);
        }
      }
      
      for (final key in keysToDelete) {
        await box.delete(key);
      }
      
      _logger.i('Deleted all credentials for ${provider.name} (${keysToDelete.length} accounts)');
    } catch (e, stack) {
      _logger.e('Failed to delete all credentials for ${provider.name}', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get all credentials across all providers
  Future<List<UserCredentials>> getAllCredentials() async {
    try {
      final box = await _getBox();
      final allCredentials = box.values.toList();
      
      // Sort by last used (most recent first)
      allCredentials.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));
      
      _logger.d('Found ${allCredentials.length} total credentials');
      return allCredentials;
    } catch (e, stack) {
      _logger.e('Failed to get all credentials', error: e, stackTrace: stack);
      return [];
    }
  }

  /// Get credentials count for each provider
  Future<Map<EmailProvider, int>> getCredentialsCount() async {
    try {
      final box = await _getBox();
      final counts = <EmailProvider, int>{};
      
      for (final provider in EmailProvider.values) {
        counts[provider] = box.values.where((cred) => cred.provider == provider).length;
      }
      
      return counts;
    } catch (e, stack) {
      _logger.e('Failed to get credentials count', error: e, stackTrace: stack);
      return {};
    }
  }

  /// Clear all credentials (for testing or reset purposes)
  Future<void> clearAllCredentials() async {
    try {
      final box = await _getBox();
      await box.clear();
      _logger.w('Cleared all credentials');
    } catch (e, stack) {
      _logger.e('Failed to clear all credentials', error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Migrate existing credentials from FlutterSecureStorage to Hive
  Future<void> _migrateFromSecureStorage() async {
    try {
      _logger.i('Starting migration from FlutterSecureStorage to Hive...');
      
      // Check if migration has already been done
      final box = await _getBox();
      if (box.isNotEmpty) {
        _logger.d('Credentials already exist in Hive, skipping migration');
        return;
      }

      // Migrate GMX credentials
      await _migrateGmxCredentials();
      
      // Migrate Yahoo credentials
      await _migrateYahooCredentials();
      
      // Migrate iCloud credentials
      await _migrateIcloudCredentials();
      
      _logger.i('Migration from FlutterSecureStorage completed');
    } catch (e, stack) {
      _logger.e('Failed to migrate from FlutterSecureStorage', error: e, stackTrace: stack);
      // Don't rethrow - migration failure shouldn't prevent app startup
    }
  }

  /// Migrate GMX credentials from FlutterSecureStorage
  Future<void> _migrateGmxCredentials() async {
    try {
      final email = await _secureStorage.read(key: 'gmx_email');
      final password = await _secureStorage.read(key: 'gmx_password');
      
      if (email != null && password != null) {
        final displayName = await _secureStorage.read(key: 'gmx_display_name') ?? email.split('@')[0];
        final imapHost = await _secureStorage.read(key: 'gmx_imap_host') ?? 'imap.gmx.com';
        final imapPort = int.tryParse(await _secureStorage.read(key: 'gmx_imap_port') ?? '993') ?? 993;
        final smtpHost = await _secureStorage.read(key: 'gmx_smtp_host') ?? 'mail.gmx.com';
        final smtpPort = int.tryParse(await _secureStorage.read(key: 'gmx_smtp_port') ?? '587') ?? 587;
        final useSSL = (await _secureStorage.read(key: 'gmx_use_ssl') ?? 'true').toLowerCase() == 'true';
        
        final credentials = UserCredentials(
          id: UserCredentials.generateId(EmailProvider.gmx, email),
          provider: EmailProvider.gmx,
          email: email,
          password: password,
          displayName: displayName,
          imapHost: imapHost,
          imapPort: imapPort,
          smtpHost: smtpHost,
          smtpPort: smtpPort,
          useSSL: useSSL,
          createdAt: DateTime.now(),
          lastUsed: DateTime.now(),
        );
        
        await saveCredentials(credentials);
        _logger.i('Migrated GMX credentials for: $email');
      }
    } catch (e, stack) {
      _logger.e('Failed to migrate GMX credentials', error: e, stackTrace: stack);
    }
  }

  /// Migrate Yahoo credentials from FlutterSecureStorage
  Future<void> _migrateYahooCredentials() async {
    try {
      final email = await _secureStorage.read(key: 'yahoo_email');
      final appPassword = await _secureStorage.read(key: 'yahoo_app_password');

      if (email != null && appPassword != null) {
        final displayName = await _secureStorage.read(key: 'yahoo_display_name') ?? email.split('@')[0];
        final imapHost = await _secureStorage.read(key: 'yahoo_imap_host') ?? 'imap.mail.yahoo.com';
        final imapPort = int.tryParse(await _secureStorage.read(key: 'yahoo_imap_port') ?? '993') ?? 993;
        final smtpHost = await _secureStorage.read(key: 'yahoo_smtp_host') ?? 'smtp.mail.yahoo.com';
        final smtpPort = int.tryParse(await _secureStorage.read(key: 'yahoo_smtp_port') ?? '465') ?? 465;
        final useSSL = (await _secureStorage.read(key: 'yahoo_use_ssl') ?? 'true').toLowerCase() == 'true';

        final credentials = UserCredentials(
          id: UserCredentials.generateId(EmailProvider.yahoo, email),
          provider: EmailProvider.yahoo,
          email: email,
          password: appPassword,
          displayName: displayName,
          imapHost: imapHost,
          imapPort: imapPort,
          smtpHost: smtpHost,
          smtpPort: smtpPort,
          useSSL: useSSL,
          createdAt: DateTime.now(),
          lastUsed: DateTime.now(),
        );

        await saveCredentials(credentials);
        _logger.i('Migrated Yahoo credentials for: $email');
      }
    } catch (e, stack) {
      _logger.e('Failed to migrate Yahoo credentials', error: e, stackTrace: stack);
    }
  }

  /// Migrate iCloud credentials from FlutterSecureStorage
  Future<void> _migrateIcloudCredentials() async {
    try {
      final email = await _secureStorage.read(key: 'icloud_email');
      final appPassword = await _secureStorage.read(key: 'icloud_app_password');

      if (email != null && appPassword != null) {
        final displayName = await _secureStorage.read(key: 'icloud_display_name') ?? email.split('@')[0];
        final imapHost = await _secureStorage.read(key: 'icloud_imap_host') ?? 'imap.mail.me.com';
        final imapPort = int.tryParse(await _secureStorage.read(key: 'icloud_imap_port') ?? '993') ?? 993;
        final smtpHost = await _secureStorage.read(key: 'icloud_smtp_host') ?? 'smtp.mail.me.com';
        final smtpPort = int.tryParse(await _secureStorage.read(key: 'icloud_smtp_port') ?? '587') ?? 587;
        final useSSL = (await _secureStorage.read(key: 'icloud_use_ssl') ?? 'true').toLowerCase() == 'true';

        final credentials = UserCredentials(
          id: UserCredentials.generateId(EmailProvider.icloud, email),
          provider: EmailProvider.icloud,
          email: email,
          password: appPassword,
          displayName: displayName,
          imapHost: imapHost,
          imapPort: imapPort,
          smtpHost: smtpHost,
          smtpPort: smtpPort,
          useSSL: useSSL,
          createdAt: DateTime.now(),
          lastUsed: DateTime.now(),
        );

        await saveCredentials(credentials);
        _logger.i('Migrated iCloud credentials for: $email');
      }
    } catch (e, stack) {
      _logger.e('Failed to migrate iCloud credentials', error: e, stackTrace: stack);
    }
  }

  /// Get statistics about stored credentials
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final allCredentials = await getAllCredentials();
      final counts = await getCredentialsCount();

      return {
        'totalAccounts': allCredentials.length,
        'activeAccounts': allCredentials.where((c) => c.isActive).length,
        'providerCounts': counts.map((k, v) => MapEntry(k.name, v)),
        'oldestAccount': allCredentials.isNotEmpty
          ? allCredentials.map((c) => c.createdAt).reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String()
          : null,
        'newestAccount': allCredentials.isNotEmpty
          ? allCredentials.map((c) => c.createdAt).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
          : null,
      };
    } catch (e, stack) {
      _logger.e('Failed to get credentials statistics', error: e, stackTrace: stack);
      return {};
    }
  }
}
