/*
=======================================================
= File: user_credentials.dart
= Project: LavaMail
= Description:
=   - Hive model for storing encrypted user credentials with multi-account support
=   - Supports all email providers (Gmail, Yahoo, GMX, iCloud)
=   - Provides secure storage and retrieval of authentication data
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:hive/hive.dart';
import 'package:logger/logger.dart';

part 'user_credentials.g.dart';

/// Enum for supported email providers
@HiveType(typeId: 1)
enum EmailProvider {
  @HiveField(0)
  gmail,
  @HiveField(1)
  yahoo,
  @HiveField(2)
  gmx,
  @HiveField(3)
  icloud,
}

/// Extension to convert EmailProvider to string and vice versa
extension EmailProviderExtension on EmailProvider {
  String get name {
    switch (this) {
      case EmailProvider.gmail:
        return 'gmail';
      case EmailProvider.yahoo:
        return 'yahoo';
      case EmailProvider.gmx:
        return 'gmx';
      case EmailProvider.icloud:
        return 'icloud';
    }
  }

  static EmailProvider fromString(String provider) {
    switch (provider.toLowerCase()) {
      case 'gmail':
        return EmailProvider.gmail;
      case 'yahoo':
        return EmailProvider.yahoo;
      case 'gmx':
        return EmailProvider.gmx;
      case 'icloud':
        return EmailProvider.icloud;
      default:
        throw ArgumentError('Unknown email provider: $provider');
    }
  }
}

/// Hive model for storing user credentials with encryption
@HiveType(typeId: 0)
class UserCredentials extends HiveObject {
  static final Logger _logger = Logger();

  @HiveField(0)
  String id; // Unique identifier for the credential

  @HiveField(1)
  EmailProvider provider;

  @HiveField(2)
  String email;

  @HiveField(3)
  String password; // Will be encrypted by Hive

  @HiveField(4)
  String displayName;

  @HiveField(5)
  String imapHost;

  @HiveField(6)
  int imapPort;

  @HiveField(7)
  String smtpHost;

  @HiveField(8)
  int smtpPort;

  @HiveField(9)
  bool useSSL;

  @HiveField(10)
  DateTime createdAt;

  @HiveField(11)
  DateTime lastUsed;

  @HiveField(12)
  bool isActive; // Whether this account is currently active

  @HiveField(13)
  Map<String, dynamic>? additionalData; // For provider-specific data

  UserCredentials({
    required this.id,
    required this.provider,
    required this.email,
    required this.password,
    required this.displayName,
    required this.imapHost,
    required this.imapPort,
    required this.smtpHost,
    required this.smtpPort,
    required this.useSSL,
    required this.createdAt,
    required this.lastUsed,
    this.isActive = true,
    this.additionalData,
  });

  /// Factory constructor to create credentials from provider-specific user models
  factory UserCredentials.fromGmxUser(dynamic gmxUser) {
    return UserCredentials(
      id: generateId(EmailProvider.gmx, gmxUser.email),
      provider: EmailProvider.gmx,
      email: gmxUser.email,
      password: gmxUser.password,
      displayName: gmxUser.displayName,
      imapHost: gmxUser.imapHost,
      imapPort: gmxUser.imapPort,
      smtpHost: gmxUser.smtpHost,
      smtpPort: gmxUser.smtpPort,
      useSSL: gmxUser.useSSL,
      createdAt: DateTime.now(),
      lastUsed: gmxUser.lastLogin ?? DateTime.now(),
    );
  }

  factory UserCredentials.fromYahooUser(dynamic yahooUser) {
    return UserCredentials(
      id: generateId(EmailProvider.yahoo, yahooUser.email),
      provider: EmailProvider.yahoo,
      email: yahooUser.email,
      password: yahooUser.appPassword,
      displayName: yahooUser.displayName,
      imapHost: yahooUser.imapHost,
      imapPort: yahooUser.imapPort,
      smtpHost: yahooUser.smtpHost,
      smtpPort: yahooUser.smtpPort,
      useSSL: yahooUser.useSSL,
      createdAt: DateTime.now(),
      lastUsed: yahooUser.lastLogin ?? DateTime.now(),
    );
  }

  factory UserCredentials.fromIcloudUser(dynamic icloudUser) {
    return UserCredentials(
      id: generateId(EmailProvider.icloud, icloudUser.email),
      provider: EmailProvider.icloud,
      email: icloudUser.email,
      password: icloudUser.appPassword,
      displayName: icloudUser.displayName,
      imapHost: icloudUser.imapHost,
      imapPort: icloudUser.imapPort,
      smtpHost: icloudUser.smtpHost,
      smtpPort: icloudUser.smtpPort,
      useSSL: icloudUser.useSSL,
      createdAt: DateTime.now(),
      lastUsed: icloudUser.lastLogin ?? DateTime.now(),
    );
  }

  /// Generate unique ID for credentials
  static String generateId(EmailProvider provider, String email) {
    return '${provider.name}_${email.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Update last used timestamp
  void updateLastUsed() {
    lastUsed = DateTime.now();
    save(); // Save to Hive
    _logger.d('Updated last used for ${provider.name}: $email');
  }

  /// Mark as active/inactive
  void setActive(bool active) {
    isActive = active;
    save(); // Save to Hive
    _logger.d('Set active=$active for ${provider.name}: $email');
  }

  /// Convert to JSON (without sensitive data)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'provider': provider.name,
      'email': email,
      'displayName': displayName,
      'imapHost': imapHost,
      'imapPort': imapPort,
      'smtpHost': smtpHost,
      'smtpPort': smtpPort,
      'useSSL': useSSL,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'isActive': isActive,
      'additionalData': additionalData,
    };
  }

  /// Create a copy with updated fields
  UserCredentials copyWith({
    String? id,
    EmailProvider? provider,
    String? email,
    String? password,
    String? displayName,
    String? imapHost,
    int? imapPort,
    String? smtpHost,
    int? smtpPort,
    bool? useSSL,
    DateTime? createdAt,
    DateTime? lastUsed,
    bool? isActive,
    Map<String, dynamic>? additionalData,
  }) {
    return UserCredentials(
      id: id ?? this.id,
      provider: provider ?? this.provider,
      email: email ?? this.email,
      password: password ?? this.password,
      displayName: displayName ?? this.displayName,
      imapHost: imapHost ?? this.imapHost,
      imapPort: imapPort ?? this.imapPort,
      smtpHost: smtpHost ?? this.smtpHost,
      smtpPort: smtpPort ?? this.smtpPort,
      useSSL: useSSL ?? this.useSSL,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      isActive: isActive ?? this.isActive,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'UserCredentials(id: $id, provider: ${provider.name}, email: $email, displayName: $displayName, isActive: $isActive)';
  }
}
