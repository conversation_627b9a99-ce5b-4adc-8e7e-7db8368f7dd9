// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_credentials.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class EmailProviderAdapter extends TypeAdapter<EmailProvider> {
  @override
  final int typeId = 1;

  @override
  EmailProvider read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return EmailProvider.gmail;
      case 1:
        return EmailProvider.yahoo;
      case 2:
        return EmailProvider.gmx;
      case 3:
        return EmailProvider.icloud;
      default:
        return EmailProvider.gmail;
    }
  }

  @override
  void write(BinaryWriter writer, EmailProvider obj) {
    switch (obj) {
      case EmailProvider.gmail:
        writer.writeByte(0);
        break;
      case EmailProvider.yahoo:
        writer.writeByte(1);
        break;
      case EmailProvider.gmx:
        writer.writeByte(2);
        break;
      case EmailProvider.icloud:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmailProviderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserCredentialsAdapter extends TypeAdapter<UserCredentials> {
  @override
  final int typeId = 0;

  @override
  UserCredentials read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserCredentials(
      id: fields[0] as String,
      provider: fields[1] as EmailProvider,
      email: fields[2] as String,
      password: fields[3] as String,
      displayName: fields[4] as String,
      imapHost: fields[5] as String,
      imapPort: fields[6] as int,
      smtpHost: fields[7] as String,
      smtpPort: fields[8] as int,
      useSSL: fields[9] as bool,
      createdAt: fields[10] as DateTime,
      lastUsed: fields[11] as DateTime,
      isActive: fields[12] as bool,
      additionalData: (fields[13] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, UserCredentials obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.provider)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.password)
      ..writeByte(4)
      ..write(obj.displayName)
      ..writeByte(5)
      ..write(obj.imapHost)
      ..writeByte(6)
      ..write(obj.imapPort)
      ..writeByte(7)
      ..write(obj.smtpHost)
      ..writeByte(8)
      ..write(obj.smtpPort)
      ..writeByte(9)
      ..write(obj.useSSL)
      ..writeByte(10)
      ..write(obj.createdAt)
      ..writeByte(11)
      ..write(obj.lastUsed)
      ..writeByte(12)
      ..write(obj.isActive)
      ..writeByte(13)
      ..write(obj.additionalData);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserCredentialsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
