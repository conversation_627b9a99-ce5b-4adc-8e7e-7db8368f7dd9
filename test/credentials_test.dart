/*
=======================================================
= File: credentials_test.dart
= Project: LavaMail
= Description:
=   - Test file for the new Hive-based credentials system
=   - Tests multi-account functionality and data integrity
=   - All code, comments, and documentation are in English as per project standards.
=======================================================
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:lavamail/core/auth/models/user_credentials.dart';
import 'package:lavamail/core/auth/services/credentials_manager.dart';

void main() {
  group('Credentials System Tests', () {
    late CredentialsManager credentialsManager;

    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      
      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(UserCredentialsAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(EmailProviderAdapter());
      }
      
      credentialsManager = CredentialsManager();
      await credentialsManager.initialize();
    });

    tearDownAll(() async {
      await Hive.close();
    });

    setUp(() async {
      // Clear all credentials before each test
      await credentialsManager.clearAllCredentials();
    });

    test('should save and retrieve GMX credentials', () async {
      // Create test credentials
      final credentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'testpassword',
        displayName: 'Test User',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // Save credentials
      await credentialsManager.saveCredentials(credentials);

      // Retrieve credentials
      final savedCredentials = await credentialsManager.getCredentialsForProvider(EmailProvider.gmx);

      expect(savedCredentials.length, 1);
      expect(savedCredentials.first.email, '<EMAIL>');
      expect(savedCredentials.first.provider, EmailProvider.gmx);
    });

    test('should support multiple accounts per provider', () async {
      // Create multiple GMX accounts
      final credentials1 = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'password1',
        displayName: 'User 1',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      final credentials2 = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'password2',
        displayName: 'User 2',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now().subtract(Duration(hours: 1)),
      );

      // Save both credentials
      await credentialsManager.saveCredentials(credentials1);
      await credentialsManager.saveCredentials(credentials2);

      // Retrieve credentials
      final savedCredentials = await credentialsManager.getCredentialsForProvider(EmailProvider.gmx);

      expect(savedCredentials.length, 2);
      
      // Should be sorted by last used (most recent first)
      expect(savedCredentials.first.email, '<EMAIL>');
      expect(savedCredentials.last.email, '<EMAIL>');
    });

    test('should support multiple providers', () async {
      // Create credentials for different providers
      final gmxCredentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'gmxpassword',
        displayName: 'GMX User',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      final yahooCredentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.yahoo, '<EMAIL>'),
        provider: EmailProvider.yahoo,
        email: '<EMAIL>',
        password: 'yahooapppassword',
        displayName: 'Yahoo User',
        imapHost: 'imap.mail.yahoo.com',
        imapPort: 993,
        smtpHost: 'smtp.mail.yahoo.com',
        smtpPort: 465,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // Save credentials
      await credentialsManager.saveCredentials(gmxCredentials);
      await credentialsManager.saveCredentials(yahooCredentials);

      // Retrieve credentials by provider
      final gmxSaved = await credentialsManager.getCredentialsForProvider(EmailProvider.gmx);
      final yahooSaved = await credentialsManager.getCredentialsForProvider(EmailProvider.yahoo);

      expect(gmxSaved.length, 1);
      expect(yahooSaved.length, 1);
      expect(gmxSaved.first.email, '<EMAIL>');
      expect(yahooSaved.first.email, '<EMAIL>');
    });

    test('should get statistics correctly', () async {
      // Create test data
      final gmxCredentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'password',
        displayName: 'Test User',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
        isActive: false, // Inactive account
      );

      final yahooCredentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.yahoo, '<EMAIL>'),
        provider: EmailProvider.yahoo,
        email: '<EMAIL>',
        password: 'password',
        displayName: 'Test User',
        imapHost: 'imap.mail.yahoo.com',
        imapPort: 993,
        smtpHost: 'smtp.mail.yahoo.com',
        smtpPort: 465,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
        isActive: true, // Active account
      );

      await credentialsManager.saveCredentials(gmxCredentials);
      await credentialsManager.saveCredentials(yahooCredentials);

      // Get statistics
      final stats = await credentialsManager.getStatistics();

      expect(stats['totalAccounts'], 2);
      expect(stats['activeAccounts'], 1);
      expect(stats['providerCounts']['gmx'], 1);
      expect(stats['providerCounts']['yahoo'], 1);
    });

    test('should delete credentials correctly', () async {
      // Create and save credentials
      final credentials = UserCredentials(
        id: UserCredentials.generateId(EmailProvider.gmx, '<EMAIL>'),
        provider: EmailProvider.gmx,
        email: '<EMAIL>',
        password: 'password',
        displayName: 'Test User',
        imapHost: 'imap.gmx.com',
        imapPort: 993,
        smtpHost: 'mail.gmx.com',
        smtpPort: 587,
        useSSL: true,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await credentialsManager.saveCredentials(credentials);

      // Verify it exists
      var savedCredentials = await credentialsManager.getCredentialsForProvider(EmailProvider.gmx);
      expect(savedCredentials.length, 1);

      // Delete it
      await credentialsManager.deleteCredentials(credentials.id);

      // Verify it's gone
      savedCredentials = await credentialsManager.getCredentialsForProvider(EmailProvider.gmx);
      expect(savedCredentials.length, 0);
    });
  });
}
